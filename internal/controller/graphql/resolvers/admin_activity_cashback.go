package resolvers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	admin_gql_model "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	repo_activity_cashback "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

// CreateTask creates a new activity task (Admin only)
func (r *ActivityCashbackResolver) CreateTask(ctx context.Context, input admin_gql_model.CreateTaskInput) (*admin_gql_model.ActivityTask, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	adminUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	// TODO: Add admin role check here
	// For now, we assume the user is admin if they have a valid token

	adminService := activity_cashback.NewAdminService()

	// Parse category ID
	categoryID, err := strconv.ParseUint(input.CategoryID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid category ID: %w", err)
	}

	// Create task model
	task := &model.ActivityTask{
		CategoryID: uint(categoryID),
		Name:       input.Name,
		TaskType:   model.TaskType(input.TaskType),
		Frequency:  model.TaskFrequency(input.Frequency),
		Points:     input.Points,
		IsActive:   true,
		SortOrder:  0, // Default value
	}

	// Set optional fields
	if input.SortOrder != nil {
		task.SortOrder = *input.SortOrder
	}

	if input.Description != nil {
		task.Description = input.Description
	}

	// Set TaskIdentifier if provided
	if input.TaskIdentifier != nil {
		identifier := model.TaskIdentifier(string(*input.TaskIdentifier))
		// Validate that the identifier is valid
		if model.IsValidTaskIdentifier(identifier) {
			task.TaskIdentifier = &identifier
		} else {
			return nil, fmt.Errorf("invalid task identifier: %s", string(*input.TaskIdentifier))
		}
	}
	if input.MaxCompletions != nil {
		task.MaxCompletions = input.MaxCompletions
	}
	if input.ResetPeriod != nil {
		resetPeriod := model.ResetPeriod(*input.ResetPeriod)
		task.ResetPeriod = &resetPeriod
	}
	if input.ActionTarget != nil {
		task.ActionTarget = input.ActionTarget
	}
	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		task.VerificationMethod = &verificationMethod
	}
	if input.ExternalLink != nil {
		task.ExternalLink = input.ExternalLink
	}
	if input.StartDate != nil {
		task.StartDate = utils.TimestampToTime(input.StartDate)
	}
	if input.EndDate != nil {
		task.EndDate = utils.TimestampToTime(input.EndDate)
	}

	// Create the task
	if err := adminService.CreateTask(ctx, task, adminUUID); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err))
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	// Convert to GraphQL model
	gqlTask := convertActivityTaskToGQL(task)
	adminTask := convertUserTaskToAdminTask(gqlTask)
	return adminTask, nil
}

// UpdateTask updates an existing activity task (Admin only)
func (r *ActivityCashbackResolver) UpdateTask(ctx context.Context, input admin_gql_model.UpdateTaskInput) (*admin_gql_model.ActivityTask, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	adminUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	adminService := activity_cashback.NewAdminService()

	// Parse task ID
	taskUUID, err := uuid.Parse(input.ID)
	if err != nil {
		return nil, fmt.Errorf("invalid task ID: %w", err)
	}

	// Get existing task
	tasks, err := adminService.GetAllTasks(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks: %w", err)
	}

	var existingTask *model.ActivityTask
	for _, task := range tasks {
		if task.ID == taskUUID {
			existingTask = &task
			break
		}
	}

	if existingTask == nil {
		return nil, fmt.Errorf("task not found")
	}

	// Update fields if provided
	if input.CategoryID != nil {
		categoryID, err := strconv.ParseUint(*input.CategoryID, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid category ID: %w", err)
		}
		existingTask.CategoryID = uint(categoryID)
	}
	if input.Name != nil {
		existingTask.Name = *input.Name
	}
	if input.Description != nil {
		existingTask.Description = input.Description
	}
	if input.TaskType != nil {
		existingTask.TaskType = model.TaskType(*input.TaskType)
	}
	if input.Frequency != nil {
		existingTask.Frequency = model.TaskFrequency(*input.Frequency)
	}
	if input.Points != nil {
		existingTask.Points = *input.Points
	}
	if input.MaxCompletions != nil {
		existingTask.MaxCompletions = input.MaxCompletions
	}
	if input.ResetPeriod != nil {
		resetPeriod := model.ResetPeriod(*input.ResetPeriod)
		existingTask.ResetPeriod = &resetPeriod
	}
	if input.ActionTarget != nil {
		existingTask.ActionTarget = input.ActionTarget
	}
	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		existingTask.VerificationMethod = &verificationMethod
	}
	if input.ExternalLink != nil {
		existingTask.ExternalLink = input.ExternalLink
	}
	if input.IsActive != nil {
		existingTask.IsActive = *input.IsActive
	}
	if input.StartDate != nil {
		existingTask.StartDate = utils.TimestampToTime(input.StartDate)
	}
	if input.EndDate != nil {
		existingTask.EndDate = utils.TimestampToTime(input.EndDate)
	}
	if input.SortOrder != nil {
		existingTask.SortOrder = *input.SortOrder
	}

	// Update the task
	if err := adminService.UpdateTask(ctx, existingTask, adminUUID); err != nil {
		global.GVA_LOG.Error("Failed to update task", zap.Error(err))
		return nil, fmt.Errorf("failed to update task: %w", err)
	}

	// Convert to GraphQL model
	gqlTask := convertActivityTaskToGQL(existingTask)
	adminTask := convertUserTaskToAdminTask(gqlTask)
	return adminTask, nil
}

// DeleteTask deletes an activity task (Admin only)
func (r *ActivityCashbackResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return false, utils.ErrAccessTokenInvalid
	}

	adminService := activity_cashback.NewAdminService()

	// Parse task ID
	taskUUID, err := uuid.Parse(taskID)
	if err != nil {
		return false, fmt.Errorf("invalid task ID: %w", err)
	}

	// Delete the task
	if err := adminService.DeleteTask(ctx, taskUUID); err != nil {
		global.GVA_LOG.Error("Failed to delete task", zap.Error(err))
		return false, fmt.Errorf("failed to delete task: %w", err)
	}

	return true, nil
}

// CreateTierBenefit creates a new tier benefit (Admin only)
func (r *ActivityCashbackResolver) CreateTierBenefit(ctx context.Context, input admin_gql_model.CreateTierBenefitInput) (*admin_gql_model.TierBenefitResponse, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Access token invalid",
		}, nil
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid user ID format",
		}, nil
	}

	_, err := uuid.Parse(userIDStr)
	if err != nil {
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid admin ID",
		}, nil
	}

	adminService := activity_cashback.NewAdminService()

	// Create tier benefit model
	benefit := &model.TierBenefit{
		TierLevel:          input.TierLevel,
		TierName:           input.TierName,
		MinPoints:          input.MinPoints,
		CashbackPercentage: decimal.NewFromFloat(input.CashbackPercentage),
		IsActive:           true,
	}

	// Set optional fields
	if input.BenefitsDescription != nil {
		benefit.BenefitsDescription = input.BenefitsDescription
	}
	if input.TierColor != nil {
		benefit.TierColor = input.TierColor
	}
	if input.TierIcon != nil {
		benefit.TierIcon = input.TierIcon
	}

	// Create the tier benefit
	if err := adminService.CreateTierBenefit(ctx, benefit); err != nil {
		global.GVA_LOG.Error("Failed to create tier benefit", zap.Error(err))
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Failed to create tier benefit",
		}, nil
	}

	return &admin_gql_model.TierBenefitResponse{
		Success: true,
		Message: "Tier benefit created successfully",
		Data:    convertTierBenefitToAdminGQL(benefit),
	}, nil
}

// UpdateTierBenefit updates an existing tier benefit (Admin only)
func (r *ActivityCashbackResolver) UpdateTierBenefit(ctx context.Context, input admin_gql_model.UpdateTierBenefitInput) (*admin_gql_model.TierBenefitResponse, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Access token invalid",
		}, nil
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid user ID format",
		}, nil
	}

	_, err := uuid.Parse(userIDStr)
	if err != nil {
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid admin ID",
		}, nil
	}

	adminService := activity_cashback.NewAdminService()

	// Parse tier benefit ID
	benefitID, err := strconv.ParseUint(input.ID, 10, 32)
	if err != nil {
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid tier benefit ID",
		}, nil
	}

	// Get existing tier benefit
	tierBenefitRepo := repo_activity_cashback.NewTierBenefitRepository()
	existingBenefit, err := tierBenefitRepo.GetByID(ctx, uint(benefitID))
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier benefit", zap.Error(err))
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Tier benefit not found",
		}, nil
	}

	// Update fields if provided
	if input.TierLevel != nil {
		existingBenefit.TierLevel = *input.TierLevel
	}
	if input.TierName != nil {
		existingBenefit.TierName = *input.TierName
	}
	if input.MinPoints != nil {
		existingBenefit.MinPoints = *input.MinPoints
	}
	if input.CashbackPercentage != nil {
		existingBenefit.CashbackPercentage = decimal.NewFromFloat(*input.CashbackPercentage)
	}
	if input.BenefitsDescription != nil {
		existingBenefit.BenefitsDescription = input.BenefitsDescription
	}
	if input.TierColor != nil {
		existingBenefit.TierColor = input.TierColor
	}
	if input.TierIcon != nil {
		existingBenefit.TierIcon = input.TierIcon
	}
	if input.IsActive != nil {
		existingBenefit.IsActive = *input.IsActive
	}

	// Update the tier benefit
	if err := adminService.UpdateTierBenefit(ctx, existingBenefit); err != nil {
		global.GVA_LOG.Error("Failed to update tier benefit", zap.Error(err))
		return &admin_gql_model.TierBenefitResponse{
			Success: false,
			Message: "Failed to update tier benefit",
		}, nil
	}

	return &admin_gql_model.TierBenefitResponse{
		Success: true,
		Message: "Tier benefit updated successfully",
		Data:    convertTierBenefitToAdminGQL(existingBenefit),
	}, nil
}

// DeleteTierBenefit deletes a tier benefit (Admin only)
func (r *ActivityCashbackResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	// Check admin permissions
	userID := ctx.Value("userId")
	if userID == nil {
		return false, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return false, utils.ErrAccessTokenInvalid
	}

	_, err := uuid.Parse(userIDStr)
	if err != nil {
		return false, fmt.Errorf("invalid admin ID: %w", err)
	}

	adminService := activity_cashback.NewAdminService()

	// Parse tier benefit ID
	benefitID, err := strconv.ParseUint(tierBenefitID, 10, 32)
	if err != nil {
		return false, fmt.Errorf("invalid tier benefit ID: %w", err)
	}

	// Delete the tier benefit
	if err := adminService.DeleteTierBenefit(ctx, uint(benefitID)); err != nil {
		global.GVA_LOG.Error("Failed to delete tier benefit", zap.Error(err))
		return false, fmt.Errorf("failed to delete tier benefit: %w", err)
	}

	return true, nil
}

// TierBenefits retrieves all tier benefits
func (r *ActivityCashbackResolver) TierBenefits(ctx context.Context) (*gql_model.TierBenefitsResponse, error) {
	adminService := activity_cashback.NewAdminService()

	benefits, err := adminService.GetTierBenefits(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier benefits", zap.Error(err))
		return &gql_model.TierBenefitsResponse{
			Success: false,
			Message: "Failed to retrieve tier benefits",
		}, nil
	}

	var gqlBenefits []*gql_model.TierBenefit
	for _, benefit := range benefits {
		gqlBenefits = append(gqlBenefits, convertTierBenefitToGQL(&benefit))
	}

	return &gql_model.TierBenefitsResponse{
		Success: true,
		Message: "Tier benefits retrieved successfully",
		Data:    gqlBenefits,
	}, nil
}

// UserTaskProgress retrieves user task progress
func (r *ActivityCashbackResolver) UserTaskProgress(ctx context.Context) (*gql_model.UserTaskProgressResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	progress, err := service.GetUserTaskProgress(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user task progress", zap.Error(err))
		return &gql_model.UserTaskProgressResponse{
			Success: false,
			Message: "Failed to retrieve task progress",
		}, nil
	}

	var gqlProgress []*gql_model.UserTaskProgress
	for _, p := range progress {
		gqlProgress = append(gqlProgress, convertUserTaskProgressToGQL(&p))
	}

	return &gql_model.UserTaskProgressResponse{
		Success: true,
		Message: "Task progress retrieved successfully",
		Data:    gqlProgress,
	}, nil
}

// TaskCategories retrieves all task categories
func (r *ActivityCashbackResolver) TaskCategories(ctx context.Context) ([]*gql_model.TaskCategory, error) {
	adminService := activity_cashback.NewAdminService()

	categories, err := adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	var gqlCategories []*gql_model.TaskCategory
	for _, category := range categories {
		gqlCategories = append(gqlCategories, convertTaskCategoryToGQL(&category))
	}

	return gqlCategories, nil
}

// TasksByCategory retrieves tasks by category name
func (r *ActivityCashbackResolver) TasksByCategory(ctx context.Context, categoryName string) ([]*gql_model.ActivityTask, error) {
	service := activity_cashback.NewActivityCashbackService()

	tasks, err := service.GetTasksByCategory(ctx, categoryName)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tasks by category", zap.Error(err))
		return nil, fmt.Errorf("failed to get tasks by category: %w", err)
	}

	var gqlTasks []*gql_model.ActivityTask
	for _, task := range tasks {
		gqlTasks = append(gqlTasks, convertActivityTaskToGQL(&task))
	}

	return gqlTasks, nil
}

// AdminGetAllTasks retrieves all tasks (Admin only)
func (r *ActivityCashbackResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	adminService := activity_cashback.NewAdminService()

	tasks, err := adminService.GetAllTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get all tasks", zap.Error(err))
		return nil, fmt.Errorf("failed to get all tasks: %w", err)
	}

	var gqlTasks []*gql_model.ActivityTask
	for _, task := range tasks {
		gqlTasks = append(gqlTasks, convertActivityTaskToGQL(&task))
	}

	return gqlTasks, nil
}

// AdminGetTaskCompletionStats retrieves task completion statistics (Admin only)
func (r *ActivityCashbackResolver) AdminGetTaskCompletionStats(ctx context.Context, input admin_gql_model.AdminStatsInput) (*admin_gql_model.AdminTaskCompletionStatsResponse, error) {
	adminService := activity_cashback.NewAdminService()

	stats, err := adminService.GetTaskCompletionStats(ctx, input.StartDate, input.EndDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task completion stats", zap.Error(err))
		return &admin_gql_model.AdminTaskCompletionStatsResponse{
			Success: false,
			Message: "Failed to retrieve task completion statistics",
		}, nil
	}

	// Convert stats to GraphQL format
	var taskCompletions []*admin_gql_model.TaskCompletionStat
	if taskStats, ok := stats["task_completions"].(map[string]int); ok {
		for taskName, count := range taskStats {
			taskCompletions = append(taskCompletions, &admin_gql_model.TaskCompletionStat{
				TaskName:        taskName,
				CompletionCount: count,
			})
		}
	}

	totalTasks := 0
	if total, ok := stats["total_tasks"].(int); ok {
		totalTasks = total
	}

	return &admin_gql_model.AdminTaskCompletionStatsResponse{
		Success: true,
		Message: "Task completion statistics retrieved successfully",
		Data: &admin_gql_model.AdminTaskCompletionStats{
			TaskCompletions: taskCompletions,
			StartDate:       input.StartDate,
			EndDate:         input.EndDate,
			TotalTasks:      totalTasks,
		},
	}, nil
}

// AdminGetUserActivityStats retrieves user activity statistics (Admin only)
func (r *ActivityCashbackResolver) AdminGetUserActivityStats(ctx context.Context, input admin_gql_model.AdminStatsInput) (*admin_gql_model.AdminUserActivityStatsResponse, error) {
	adminService := activity_cashback.NewAdminService()

	stats, err := adminService.GetUserActivityStats(ctx, input.StartDate, input.EndDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user activity stats", zap.Error(err))
		return &admin_gql_model.AdminUserActivityStatsResponse{
			Success: false,
			Message: "Failed to retrieve user activity statistics",
		}, nil
	}

	// Convert stats to GraphQL format
	var dailyCompletions []*admin_gql_model.DailyCompletionStat
	if dailyStats, ok := stats["daily_completions"].(map[string]int); ok {
		for date, count := range dailyStats {
			dailyCompletions = append(dailyCompletions, &admin_gql_model.DailyCompletionStat{
				Date:            date,
				CompletionCount: count,
			})
		}
	}

	return &admin_gql_model.AdminUserActivityStatsResponse{
		Success: true,
		Message: "User activity statistics retrieved successfully",
		Data: &admin_gql_model.AdminUserActivityStats{
			DailyCompletions: dailyCompletions,
			StartDate:        input.StartDate,
			EndDate:          input.EndDate,
		},
	}, nil
}

// AdminGetTierDistribution retrieves tier distribution statistics (Admin only)
func (r *ActivityCashbackResolver) AdminGetTierDistribution(ctx context.Context) (*admin_gql_model.AdminTierDistributionResponse, error) {
	adminService := activity_cashback.NewAdminService()

	distribution, err := adminService.GetTierDistribution(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier distribution", zap.Error(err))
		return &admin_gql_model.AdminTierDistributionResponse{
			Success: false,
			Message: "Failed to retrieve tier distribution",
		}, nil
	}

	// Convert distribution to GraphQL format
	var tierStats []*admin_gql_model.TierDistributionStat
	for tierLevel, userCount := range distribution {
		tierStats = append(tierStats, &admin_gql_model.TierDistributionStat{
			TierLevel: tierLevel,
			UserCount: userCount,
		})
	}

	return &admin_gql_model.AdminTierDistributionResponse{
		Success: true,
		Message: "Tier distribution retrieved successfully",
		Data:    tierStats,
	}, nil
}

// AdminGetTopUsers retrieves top users by points (Admin only)
func (r *ActivityCashbackResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*admin_gql_model.UserTierInfo, error) {
	adminService := activity_cashback.NewAdminService()

	userLimit := 10 // default
	if limit != nil {
		userLimit = *limit
	}

	users, err := adminService.GetTopUsers(ctx, userLimit)
	if err != nil {
		global.GVA_LOG.Error("Failed to get top users", zap.Error(err))
		return nil, fmt.Errorf("failed to get top users: %w", err)
	}

	var gqlUsers []*admin_gql_model.UserTierInfo
	for _, user := range users {
		gqlUsers = append(gqlUsers, convertUserTierInfoToAdminGQL(&user))
	}

	return gqlUsers, nil
}

// AdminResetDailyTasks resets all daily tasks (Admin only)
func (r *ActivityCashbackResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	adminService := activity_cashback.NewAdminService()

	if err := adminService.ResetAllDailyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset daily tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset daily tasks: %w", err)
	}

	return true, nil
}

// AdminResetWeeklyTasks resets all weekly tasks (Admin only)
func (r *ActivityCashbackResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	adminService := activity_cashback.NewAdminService()

	if err := adminService.ResetAllWeeklyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset weekly tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset weekly tasks: %w", err)
	}

	return true, nil
}

// AdminResetMonthlyTasks resets all monthly tasks (Admin only)
func (r *ActivityCashbackResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	adminService := activity_cashback.NewAdminService()

	if err := adminService.ResetAllMonthlyTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to reset monthly tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset monthly tasks: %w", err)
	}

	return true, nil
}

// AdminRecalculateAllUserTiers recalculates all user tiers (Admin only)
func (r *ActivityCashbackResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	adminService := activity_cashback.NewAdminService()

	if err := adminService.RecalculateAllUserTiers(ctx); err != nil {
		global.GVA_LOG.Error("Failed to recalculate all user tiers", zap.Error(err))
		return false, fmt.Errorf("failed to recalculate all user tiers: %w", err)
	}

	return true, nil
}

// AdminSeedInitialTasks seeds initial tasks (Admin only)
func (r *ActivityCashbackResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	adminService := activity_cashback.NewAdminService()

	if err := adminService.SeedInitialTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to seed initial tasks", zap.Error(err))
		return false, fmt.Errorf("failed to seed initial tasks: %w", err)
	}

	return true, nil
}

// UserTierInfo retrieves user tier information
func (r *ActivityCashbackResolver) UserTierInfo(ctx context.Context) (*gql_model.UserTierInfo, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	tierInfo, err := service.GetUserTierInfo(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user tier info", zap.Error(err))
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	return convertUserTierInfoToGQL(tierInfo), nil
}

// TaskCompletionHistory retrieves task completion history
func (r *ActivityCashbackResolver) TaskCompletionHistory(ctx context.Context, input *gql_model.TaskCompletionHistoryInput) (*gql_model.TaskCompletionHistoryResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Set default values
	limit := 10
	offset := 0
	if input != nil {
		if input.Limit != nil {
			limit = *input.Limit
		}
		if input.Offset != nil {
			offset = *input.Offset
		}
	}

	// Use factory pattern to get unified repository
	factory := repo_activity_cashback.NewTaskCompletionRepositoryFactory()
	unifiedRepo := factory.GetUnifiedRepository()

	// Get completion history for the user
	completions, err := unifiedRepo.GetByUserID(ctx, userUUID, limit, offset)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task completion history", zap.Error(err))
		return &gql_model.TaskCompletionHistoryResponse{
			Success: false,
			Message: "Failed to retrieve completion history",
		}, nil
	}

	// Convert to GraphQL format
	var gqlHistory []*gql_model.TaskCompletionHistory
	for _, completion := range completions {
		gqlCompletion := &gql_model.TaskCompletionHistory{
			ID:             completion.GetID().String(),
			UserID:         completion.GetUserID().String(),
			TaskID:         completion.GetTaskID().String(),
			PointsAwarded:  completion.GetPointsAwarded(),
			CompletionDate: completion.GetCompletionDate(),
			CreatedAt:      completion.GetCreatedAt(),
		}

		// Add verification data if available
		if verificationData := completion.GetVerificationData(); verificationData != nil {
			verificationJSON, _ := json.Marshal(verificationData)
			verificationStr := string(verificationJSON)
			gqlCompletion.VerificationData = &verificationStr
		}

		// Add task information if available
		task := completion.GetTask()
		if task.ID != uuid.Nil {
			gqlTask := convertActivityTaskToGQL(&task)
			gqlCompletion.Task = gqlTask
		}

		gqlHistory = append(gqlHistory, gqlCompletion)
	}

	return &gql_model.TaskCompletionHistoryResponse{
		Success: true,
		Message: "Completion history retrieved successfully",
		Data:    gqlHistory,
		Total:   len(gqlHistory),
	}, nil
}

// Helper functions to convert between user and admin GraphQL models
func convertUserTaskToAdminTask(userTask *gql_model.ActivityTask) *admin_gql_model.ActivityTask {
	if userTask == nil {
		return nil
	}

	adminTask := &admin_gql_model.ActivityTask{
		ID:                 userTask.ID,
		CategoryID:         userTask.CategoryID,
		Name:               userTask.Name,
		Description:        userTask.Description,
		TaskType:           admin_gql_model.TaskType(userTask.TaskType),
		Frequency:          admin_gql_model.TaskFrequency(userTask.Frequency),
		Points:             userTask.Points,
		MaxCompletions:     userTask.MaxCompletions,
		ResetPeriod:        userTask.ResetPeriod,
		Conditions:         userTask.Conditions,
		ActionTarget:       userTask.ActionTarget,
		VerificationMethod: userTask.VerificationMethod,
		ExternalLink:       userTask.ExternalLink,
		IsActive:           userTask.IsActive,
		StartDate:          userTask.StartDate,
		EndDate:            userTask.EndDate,
		SortOrder:          userTask.SortOrder,
		CreatedAt:          userTask.CreatedAt,
		UpdatedAt:          userTask.UpdatedAt,
	}

	if userTask.TaskIdentifier != nil {
		taskIdentifier := admin_gql_model.TaskIdentifier(*userTask.TaskIdentifier)
		adminTask.TaskIdentifier = &taskIdentifier
	}

	// Category field removed from user GraphQL schema to prevent circular reference

	return adminTask
}

func convertUserCategoryToAdminCategory(userCategory *gql_model.TaskCategory) *admin_gql_model.TaskCategory {
	if userCategory == nil {
		return nil
	}

	return &admin_gql_model.TaskCategory{
		ID:          userCategory.ID,
		Name:        userCategory.Name,
		DisplayName: userCategory.DisplayName,
		Description: userCategory.Description,
		IsActive:    userCategory.IsActive,
		SortOrder:   userCategory.SortOrder,
		CreatedAt:   userCategory.CreatedAt,
		UpdatedAt:   userCategory.UpdatedAt,
	}
}

func convertUserTierInfoToAdminGQL(userTierInfo *model.UserTierInfo) *admin_gql_model.UserTierInfo {
	if userTierInfo == nil {
		return nil
	}

	// Convert decimal fields to float64
	availableCashback, _ := userTierInfo.ClaimableCashbackUSD.Float64()
	totalCashbackClaimed, _ := userTierInfo.ClaimedCashbackUSD.Float64()

	adminUserTierInfo := &admin_gql_model.UserTierInfo{
		UserID:               userTierInfo.UserID.String(),
		TotalPoints:          userTierInfo.TotalPoints,
		AvailableCashback:    availableCashback,
		TotalCashbackClaimed: totalCashbackClaimed,
		CreatedAt:            userTierInfo.CreatedAt,
	}

	// Set email from User relationship if available
	if userTierInfo.User.Email != nil {
		adminUserTierInfo.Email = userTierInfo.User.Email
	}

	// Set last activity date
	if userTierInfo.LastActivityDate != nil {
		adminUserTierInfo.LastActivityAt = userTierInfo.LastActivityDate
	}

	// Convert TierBenefit if available
	if userTierInfo.TierBenefit != nil {
		adminUserTierInfo.CurrentTier = convertTierBenefitToAdminGQL(userTierInfo.TierBenefit)
	}

	return adminUserTierInfo
}

func convertTierBenefitToAdminGQL(tierBenefit *model.TierBenefit) *admin_gql_model.TierBenefit {
	if tierBenefit == nil {
		return nil
	}

	// Convert decimal to float64
	cashbackPercentage, _ := tierBenefit.CashbackPercentage.Float64()

	adminTierBenefit := &admin_gql_model.TierBenefit{
		ID:                 strconv.Itoa(int(tierBenefit.ID)),
		TierLevel:          tierBenefit.TierLevel,
		TierName:           tierBenefit.TierName,
		MinPoints:          tierBenefit.MinPoints,
		CashbackPercentage: cashbackPercentage,
		IsActive:           tierBenefit.IsActive,
		CreatedAt:          tierBenefit.CreatedAt,
		UpdatedAt:          tierBenefit.UpdatedAt,
	}

	if tierBenefit.BenefitsDescription != nil {
		adminTierBenefit.BenefitsDescription = tierBenefit.BenefitsDescription
	}

	if tierBenefit.TierColor != nil {
		adminTierBenefit.TierColor = tierBenefit.TierColor
	}

	if tierBenefit.TierIcon != nil {
		adminTierBenefit.TierIcon = tierBenefit.TierIcon
	}

	return adminTierBenefit
}
